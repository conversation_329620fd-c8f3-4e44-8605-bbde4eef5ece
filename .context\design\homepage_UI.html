<html><head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>tailwind.config = {
  "theme": {
    "extend": {
      "colors": {
        "primary": "#8AB0BB",
        "secondary": "#FF8383",
        "tertiary": "#1B3E68",
        "supplement1": "#D5D8E0",
        "supplement2": "#89AFBA"
      },
      "fontFamily": {
        "sans": [
          "Inter",
          "sans-serif"
        ]
      }
    }
  }
};</script>
    <script> window.FontAwesomeConfig = { autoReplaceSvg: 'nest'};</script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    
    <style>
        ::-webkit-scrollbar { display: none;}
        body { font-family: 'Inter', sans-serif; }
        .context-card {
            transition: all 0.2s ease;
        }
        .context-card:hover {
            transform: translateY(-1px);
        }
        .glass-overlay {
            backdrop-filter: blur(10px);
            background: rgba(31, 41, 55, 0.8);
        }
    </style>
<link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin=""><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;500;600;700;800;900&amp;display=swap"><style>
  .highlighted-section {
    outline: 2px solid #3F20FB;
    background-color: rgba(63, 32, 251, 0.1);
  }

  .edit-button {
    position: absolute;
    z-index: 1000;
  }

  ::-webkit-scrollbar {
    display: none;
  }

  html, body {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  </style></head>
<body class="bg-gray-900 text-white overflow-hidden">
    
    <!-- Window Top Bar -->
    <div id="window-top-bar" class="h-6 bg-gray-950 flex items-center justify-end px-2 border-b border-gray-800">
        <div class="flex items-center gap-1">
            <button class="w-4 h-4 flex items-center justify-center hover:bg-gray-800 rounded transition-colors">
                <i class="fa-solid fa-expand text-gray-400 text-xs"></i>
            </button>
            <button class="w-4 h-4 flex items-center justify-center hover:bg-red-600 rounded transition-colors">
                <i class="fa-solid fa-xmark text-gray-400 text-xs hover:text-white"></i>
            </button>
        </div>
    </div>
    
    <!-- Top Navigation Bar -->
    <div id="top-nav" class="h-12 bg-gray-800 border-b border-tertiary flex items-center px-4">
        <div class="flex items-center gap-2">
            <div class="w-6 h-6 bg-primary rounded flex items-center justify-center">
                <i class="fa-solid fa-comment text-gray-900 text-xs"></i>
            </div>
            <span class="text-sm font-semibold text-primary">Dark Chat</span>
        </div>
        
        <div class="flex-1 flex items-center justify-center ml-12 mr-[calc(256px-48px)]">
            <div class="flex items-center gap-2 bg-tertiary/40 rounded-lg px-3 py-1 w-full max-w-md">
                <button class="p-1 hover:bg-tertiary/60 rounded transition-colors">
                    <i class="fa-solid fa-chevron-left text-gray-400 text-xs"></i>
                </button>
                <div class="flex items-center gap-2 flex-1">
                    <i class="fa-solid fa-bell text-supplement2 text-xs"></i>
                    <span class="text-xs text-supplement1">Welcome to your context hub</span>
                </div>
                <button class="p-1 hover:bg-tertiary/60 rounded transition-colors">
                    <i class="fa-solid fa-chevron-right text-gray-400 text-xs"></i>
                </button>
            </div>
        </div>
        
        <div class="flex items-center gap-3">
            <div class="flex items-center gap-2">
                <span class="text-xs text-supplement1">Private</span>
                <button class="relative inline-flex h-4 w-7 items-center rounded-full bg-secondary transition-colors">
                    <span class="inline-block h-3 w-3 transform rounded-full bg-white transition-transform translate-x-3.5"></span>
                </button>
            </div>
            <button class="p-2 hover:bg-gray-700 rounded-lg transition-colors group relative">
                <i class="fa-solid fa-user text-supplement1 text-sm"></i>
                <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    User Profile
                </div>
            </button>
            <button class="p-2 hover:bg-gray-700 rounded-lg transition-colors group relative">
                <i class="fa-solid fa-gear text-supplement1 text-sm"></i>
                <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    Settings
                </div>
            </button>
        </div>
    </div>
    
    <div id="app-container" class="flex h-[calc(100vh-72px)]">
        
        <!-- VSCode-style Icon Bar -->
        <div id="icon-bar" class="w-12 bg-gray-900 border-r border-tertiary flex flex-col items-center py-2">
            <div class="flex flex-col gap-1 mb-auto">
                <button class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative bg-primary/20 border-l-2 border-primary">
                    <i class="fa-solid fa-home text-primary"></i>
                    <div class="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        Home
                    </div>
                </button>
                <button class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative">
                    <i class="fa-solid fa-comment"></i>
                    <div class="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        Chat
                    </div>
                </button>
                <button class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative">
                    <i class="fa-solid fa-clock-rotate-left"></i>
                    <div class="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        History
                    </div>
                </button>
                <button class="w-10 h-10 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-supplement1 group relative">
                    <i class="fa-solid fa-folder-tree"></i>
                    <div class="absolute left-12 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        Files
                    </div>
                </button>
            </div>
        </div>
        
        <!-- Main Homepage Content -->
        <div id="main-content" class="flex-1 flex flex-col bg-gray-900">
            
            <!-- Search and Controls Header -->
            <div id="search-header" class="p-6 border-b border-tertiary/50">
                
                <!-- Hero Section -->
                <div id="hero-section" class="mb-8">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        
                        <!-- Start New Chat -->
                        <div class="bg-gradient-to-br from-primary/20 to-supplement2/20 border border-primary/30 rounded-xl p-6 hover:border-primary/50 transition-all">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                                    <i class="fa-solid fa-comment text-gray-900"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-primary">Start a New Chat</h3>
                            </div>
                            <p class="text-sm text-supplement1 mb-4 leading-relaxed">Something new to work on? Let's start from chatting with 300+ AI models first. Co-create your new content and then enjoy the smart context vault experience.</p>
                            <button class="flex items-center gap-2 px-4 py-2 bg-primary text-gray-900 rounded-lg font-medium hover:bg-primary/80 transition-colors">
                                <i class="fa-solid fa-comment text-sm"></i>
                                <span>New Chat</span>
                            </button>
                        </div>
                        
                        <!-- Continue Thoughts -->
                        <div class="bg-gradient-to-br from-secondary/20 to-supplement1/20 border border-secondary/30 rounded-xl p-6 hover:border-secondary/50 transition-all">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="w-10 h-10 bg-secondary rounded-lg flex items-center justify-center">
                                    <i class="fa-solid fa-arrow-rotate-right text-white"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-secondary">Continue your Thoughts</h3>
                            </div>
                            <p class="text-sm text-supplement1 mb-4 leading-relaxed">Find or select the recent context below to continue your masterpiece!</p>
                            <div class="h-10"></div> <!-- Spacer to align with other cards -->
                        </div>
                        
                        <!-- Organize Content -->
                        <div class="bg-gradient-to-br from-supplement2/20 to-tertiary/20 border border-supplement2/30 rounded-xl p-6 hover:border-supplement2/50 transition-all">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="w-10 h-10 bg-supplement2 rounded-lg flex items-center justify-center">
                                    <i class="fa-solid fa-layer-group text-gray-900"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-supplement2">Organize the Content</h3>
                            </div>
                            <p class="text-sm text-supplement1 mb-4 leading-relaxed">Let your local AI model to organize the thoughts in the file vaults</p>
                            <button class="flex items-center gap-2 px-4 py-2 bg-supplement2 text-gray-900 rounded-lg font-medium hover:bg-supplement2/80 transition-colors">
                                <i class="fa-solid fa-arrows-rotate text-sm"></i>
                                <span>Organize</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center gap-4">
                    <!-- Search Bar -->
                    <div class="flex-1 relative">
                        <i class="fa-solid fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        <input type="text" placeholder="Search contexts..." class="w-full bg-gray-800 border border-tertiary/50 rounded-lg pl-12 pr-4 py-3 text-supplement1 placeholder-gray-400 focus:outline-none focus:border-primary/50">
                    </div>
                    
                    <!-- View Toggle -->
                    <div class="flex items-center bg-gray-800 rounded-lg p-1 border border-tertiary/50">
                        <button class="p-2 rounded-md bg-primary/20 text-primary">
                            <i class="fa-solid fa-grip text-sm"></i>
                        </button>
                        <button class="p-2 rounded-md text-gray-400 hover:text-supplement1">
                            <i class="fa-solid fa-list text-sm"></i>
                        </button>
                    </div>
                    
                    <!-- Sort Toggle -->
                    <button class="flex items-center gap-2 px-4 py-3 bg-gray-800 border border-tertiary/50 rounded-lg text-supplement1 hover:bg-gray-700 transition-colors">
                        <i class="fa-solid fa-sort text-sm"></i>
                        <span class="text-sm">Recent</span>
                    </button>
                </div>
            </div>
            
            <!-- Context Cards Grid -->
            <div id="context-grid" class="flex-1 p-6 overflow-y-auto">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    
                    <!-- Context Card 1 -->
                    <div class="context-card p-4 rounded-lg border border-tertiary/50 cursor-pointer transition-all hover:bg-gray-800/50 hover:border-primary/50 bg-gradient-to-br from-primary/5 to-supplement2/5" onclick="selectContext('project-alpha')">
                        <div class="flex items-start justify-between mb-3">
                            <div class="flex-1 min-w-0">
                                <h4 class="font-medium text-sm truncate text-supplement1">Project Alpha</h4>
                                <p class="text-xs text-primary/80">Design System</p>
                            </div>
                            <div class="flex items-center gap-1 ml-2">
                                <i class="fa-solid fa-folder text-supplement2 text-sm"></i>
                                <button class="p-1 hover:bg-gray-700 rounded transition-colors group relative">
                                    <i class="fa-solid fa-info-circle text-gray-400 text-xs"></i>
                                    <div class="absolute bottom-6 left-1/2 transform -translate-x-1/2 bg-gray-800 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                                        Details
                                    </div>
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-xs text-gray-500 mb-2">
                            <span class="text-supplement2">12 chats</span>
                            <span class="text-supplement2">24 files</span>
                        </div>
                        <p class="text-xs text-gray-400 line-clamp-2">Modern design system with components, tokens, and documentation for the new product line.</p>
                        <div class="flex items-center justify-between mt-3">
                            <div class="flex items-center gap-1">
                                <div class="w-2 h-2 bg-primary rounded-full"></div>
                                <span class="text-xs text-gray-400">Updated 2h ago</span>
                            </div>
                            <button class="px-3 py-1 bg-primary text-gray-900 rounded-md text-xs font-medium hover:bg-primary/80 transition-colors">
                                View
                            </button>
                        </div>
                    </div>
                    
                    <!-- Context Card 2 -->
                    <div class="context-card p-4 rounded-lg border border-tertiary/50 cursor-pointer transition-all hover:bg-gray-800/50 hover:border-secondary/50 bg-gradient-to-br from-secondary/5 to-supplement1/5" onclick="selectContext('research-notes')">
                        <div class="flex items-start justify-between mb-3">
                            <div class="flex-1 min-w-0">
                                <h4 class="font-medium text-sm truncate text-supplement1">Research Notes</h4>
                                <p class="text-xs text-secondary/80">User Research</p>
                            </div>
                            <div class="flex items-center gap-1 ml-2">
                                <i class="fa-solid fa-lightbulb text-secondary text-sm"></i>
                                <button class="p-1 hover:bg-gray-700 rounded transition-colors group relative">
                                    <i class="fa-solid fa-info-circle text-gray-400 text-xs"></i>
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-xs text-gray-500 mb-2">
                            <span class="text-supplement1">8 chats</span>
                            <span class="text-supplement1">15 files</span>
                        </div>
                        <p class="text-xs text-gray-400 line-clamp-2">User interview insights, behavioral patterns, and usability testing results compilation.</p>
                        <div class="flex items-center justify-between mt-3">
                            <div class="flex items-center gap-1">
                                <div class="w-2 h-2 bg-secondary rounded-full"></div>
                                <span class="text-xs text-gray-400">Updated 1d ago</span>
                            </div>
                            <button class="px-3 py-1 bg-secondary text-white rounded-md text-xs font-medium hover:bg-secondary/80 transition-colors">
                                View
                            </button>
                        </div>
                    </div>
                    
                    <!-- Context Card 3 -->
                    <div class="context-card p-4 rounded-lg border border-tertiary/50 cursor-pointer transition-all hover:bg-gray-800/50 hover:border-supplement2/50 bg-gradient-to-br from-supplement2/5 to-tertiary/5" onclick="selectContext('code-review')">
                        <div class="flex items-start justify-between mb-3">
                            <div class="flex-1 min-w-0">
                                <h4 class="font-medium text-sm truncate text-supplement1">Code Review</h4>
                                <p class="text-xs text-supplement2/80">Development</p>
                            </div>
                            <div class="flex items-center gap-1 ml-2">
                                <i class="fa-solid fa-code text-supplement2 text-sm"></i>
                                <button class="p-1 hover:bg-gray-700 rounded transition-colors group relative">
                                    <i class="fa-solid fa-info-circle text-gray-400 text-xs"></i>
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-xs text-gray-500 mb-2">
                            <span class="text-primary">5 chats</span>
                            <span class="text-primary">32 files</span>
                        </div>
                        <p class="text-xs text-gray-400 line-clamp-2">Code review discussions, pull request feedback, and technical documentation.</p>
                        <div class="flex items-center justify-between mt-3">
                            <div class="flex items-center gap-1">
                                <div class="w-2 h-2 bg-supplement2 rounded-full"></div>
                                <span class="text-xs text-gray-400">Updated 3d ago</span>
                            </div>
                            <button class="px-3 py-1 bg-supplement2 text-gray-900 rounded-md text-xs font-medium hover:bg-supplement2/80 transition-colors">
                                View
                            </button>
                        </div>
                    </div>
                    
                    <!-- Context Card 4 -->
                    <div class="context-card p-4 rounded-lg border border-tertiary/50 cursor-pointer transition-all hover:bg-gray-800/50 hover:border-supplement1/50 bg-gradient-to-br from-supplement1/5 to-primary/5" onclick="selectContext('meeting-notes')">
                        <div class="flex items-start justify-between mb-3">
                            <div class="flex-1 min-w-0">
                                <h4 class="font-medium text-sm truncate text-supplement1">Meeting Notes</h4>
                                <p class="text-xs text-supplement1/80">Team Sync</p>
                            </div>
                            <div class="flex items-center gap-1 ml-2">
                                <i class="fa-solid fa-users text-supplement1 text-sm"></i>
                                <button class="p-1 hover:bg-gray-700 rounded transition-colors group relative">
                                    <i class="fa-solid fa-info-circle text-gray-400 text-xs"></i>
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-xs text-gray-500 mb-2">
                            <span class="text-secondary">3 chats</span>
                            <span class="text-secondary">7 files</span>
                        </div>
                        <p class="text-xs text-gray-400 line-clamp-2">Weekly team sync notes, action items, and project status updates.</p>
                        <div class="flex items-center justify-between mt-3">
                            <div class="flex items-center gap-1">
                                <div class="w-2 h-2 bg-supplement1 rounded-full"></div>
                                <span class="text-xs text-gray-400">Updated 5d ago</span>
                            </div>
                            <button class="px-3 py-1 bg-supplement1 text-gray-900 rounded-md text-xs font-medium hover:bg-supplement1/80 transition-colors">
                                View
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Context Details Glass Overlay -->
    <div id="context-details" class="fixed inset-0 glass-overlay hidden z-50" onclick="closeContextDetails()">
        <div class="flex items-center justify-center min-h-screen p-6">
            <div class="bg-gray-800 rounded-xl border border-tertiary/50 w-full max-w-6xl max-h-[80vh] overflow-hidden" onclick="event.stopPropagation()">
                
                <!-- Details Header -->
                <div class="flex items-center justify-between p-6 border-b border-tertiary/50">
                    <div class="flex items-center gap-3">
                        <i class="fa-solid fa-folder text-supplement2 text-lg"></i>
                        <div>
                            <h2 class="text-lg font-semibold text-supplement1">Project Alpha</h2>
                            <p class="text-sm text-gray-400">Design System Context</p>
                        </div>
                    </div>
                    <button class="p-2 hover:bg-gray-700 rounded-lg transition-colors" onclick="closeContextDetails()">
                        <i class="fa-solid fa-xmark text-gray-400"></i>
                    </button>
                </div>
                
                <!-- Details Content -->
                <div class="flex h-[60vh]">
                    
                    <!-- Recent Chats - 30% -->
                    <div class="w-[30%] border-r border-tertiary/50 flex flex-col">
                        <div class="p-4 border-b border-tertiary/50">
                            <h3 class="font-medium text-supplement1 mb-2">Recent Chats</h3>
                        </div>
                        <div class="flex-1 overflow-y-auto p-4 space-y-3">
                            <div class="p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors">
                                <p class="text-sm font-medium text-supplement1 truncate">Component Architecture</p>
                                <p class="text-xs text-gray-400 mt-1">2 hours ago</p>
                            </div>
                            <div class="p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors">
                                <p class="text-sm font-medium text-supplement1 truncate">Color Token Updates</p>
                                <p class="text-xs text-gray-400 mt-1">1 day ago</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Files - 30% -->
                    <div class="w-[30%] border-r border-tertiary/50 flex flex-col">
                        <div class="p-4 border-b border-tertiary/50">
                            <h3 class="font-medium text-supplement1 mb-2">Files</h3>
                        </div>
                        <div class="flex-1 overflow-y-auto p-4 space-y-3">
                            <div class="flex items-center gap-3 p-2 hover:bg-gray-700/50 rounded cursor-pointer">
                                <i class="fa-solid fa-file-code text-supplement2"></i>
                                <div class="flex-1">
                                    <p class="text-sm text-supplement1 truncate">tokens.json</p>
                                    <p class="text-xs text-gray-400">Design tokens</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-3 p-2 hover:bg-gray-700/50 rounded cursor-pointer">
                                <i class="fa-solid fa-image text-supplement2"></i>
                                <div class="flex-1">
                                    <p class="text-sm text-supplement1 truncate">components.fig</p>
                                    <p class="text-xs text-gray-400">Figma file</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Master.md Preview - 40% -->
                    <div class="w-[40%] flex flex-col">
                        <div class="p-4 border-b border-tertiary/50">
                            <h3 class="font-medium text-supplement1 mb-2">Master.md Preview</h3>
                        </div>
                        <div class="flex-1 overflow-y-auto p-4">
                            <div class="prose prose-invert prose-sm max-w-none">
                                <h1 class="text-supplement1">Project Alpha Design System</h1>
                                <p class="text-gray-300">A comprehensive design system for modern web applications.</p>
                                <h2 class="text-supplement1">Components</h2>
                                <ul class="text-gray-300">
                                    <li>Buttons an
</li></ul></div></div></div></div></div></div></div></body></html>